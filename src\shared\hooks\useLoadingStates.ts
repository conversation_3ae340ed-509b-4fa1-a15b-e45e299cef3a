import { useCallback, useEffect, useMemo, useState } from 'react';

export type LoadingType = 'initial' | 'page-change' | 'document-change' | 'content' | 'image';
export type LoadingPriority = 'low' | 'medium' | 'high' | 'critical';

export interface LoadingState {
  type: LoadingType;
  isLoading: boolean;
  priority: LoadingPriority;
  message?: string;
  progress?: number; // 0 to 100
  startTime: number;
  timeout?: number;
}

export interface LoadingStatesMap {
  [key: string]: LoadingState;
}

interface UseLoadingStatesOptions {
  // Default timeout for loading states (in ms)
  defaultTimeout?: number;

  // Callbacks
  onLoadingStart?: (type: LoadingType, id: string) => void;
  onLoadingEnd?: (type: LoadingType, id: string, success: boolean) => void;
  onTimeout?: (type: LoadingType, id: string) => void;

  // Display options
  showProgressBar?: boolean;
  minimumDisplayTime?: number; // Minimum time to show loading (prevents flashing)
}

/**
 * Hook for managing multiple loading states with priorities, timeouts, and progress tracking.
 * Provides a centralized way to handle loading indicators across different navigation scenarios.
 */
export function useLoadingStates({
  defaultTimeout = 10000,
  onLoadingStart,
  onLoadingEnd,
  onTimeout,
  showProgressBar = false,
  minimumDisplayTime = 200,
}: UseLoadingStatesOptions = {}) {
  const [loadingStates, setLoadingStates] = useState<LoadingStatesMap>({});
  const [timeouts, setTimeouts] = useState<Record<string, NodeJS.Timeout>>({});

  // Start a loading state
  const startLoading = useCallback(
    (
      id: string,
      type: LoadingType,
      options: {
        priority?: LoadingPriority;
        message?: string;
        timeout?: number;
        progress?: number;
      } = {},
    ) => {
      const { priority = 'medium', message, timeout = defaultTimeout, progress = 0 } = options;

      const loadingState: LoadingState = {
        type,
        isLoading: true,
        priority,
        message,
        progress,
        startTime: Date.now(),
        timeout,
      };

      setLoadingStates((prev) => ({
        ...prev,
        [id]: loadingState,
      }));

      // Set timeout if specified
      if (timeout > 0) {
        const timeoutId = setTimeout(() => {
          setLoadingStates((prev) => {
            const current = prev[id];
            if (current && current.isLoading) {
              onTimeout?.(type, id);
              return {
                ...prev,
                [id]: {
                  ...current,
                  isLoading: false,
                },
              };
            }
            return prev;
          });

          // Clean up timeout reference
          setTimeouts((prev) => {
            const newTimeouts = { ...prev };
            delete newTimeouts[id];
            return newTimeouts;
          });
        }, timeout);

        setTimeouts((prev) => ({
          ...prev,
          [id]: timeoutId,
        }));
      }

      onLoadingStart?.(type, id);
    },
    [defaultTimeout, onLoadingStart, onTimeout],
  );

  // Update loading progress
  const updateProgress = useCallback((id: string, progress: number, message?: string) => {
    setLoadingStates((prev) => {
      const current = prev[id];
      if (current && current.isLoading) {
        return {
          ...prev,
          [id]: {
            ...current,
            progress: Math.max(0, Math.min(100, progress)),
            message: message || current.message,
          },
        };
      }
      return prev;
    });
  }, []);

  // End a loading state
  const endLoading = useCallback(
    (id: string, success: boolean = true) => {
      setLoadingStates((prev) => {
        const current = prev[id];
        if (!current) return prev;

        const elapsedTime = Date.now() - current.startTime;
        const remainingTime = Math.max(0, minimumDisplayTime - elapsedTime);

        if (remainingTime > 0) {
          // Delay ending to meet minimum display time
          setTimeout(() => {
            setLoadingStates((prevInner) => ({
              ...prevInner,
              [id]: {
                ...current,
                isLoading: false,
                progress: 100,
              },
            }));
            onLoadingEnd?.(current.type, id, success);
          }, remainingTime);
        } else {
          // End immediately
          onLoadingEnd?.(current.type, id, success);
          return {
            ...prev,
            [id]: {
              ...current,
              isLoading: false,
              progress: 100,
            },
          };
        }

        return prev;
      });

      // Clear timeout if it exists
      const timeoutId = timeouts[id];
      if (timeoutId) {
        clearTimeout(timeoutId);
        setTimeouts((prev) => {
          const newTimeouts = { ...prev };
          delete newTimeouts[id];
          return newTimeouts;
        });
      }
    },
    [minimumDisplayTime, onLoadingEnd, timeouts],
  );

  // Clear a loading state completely
  const clearLoading = useCallback(
    (id: string) => {
      setLoadingStates((prev) => {
        const newStates = { ...prev };
        delete newStates[id];
        return newStates;
      });

      // Clear timeout if it exists
      const timeoutId = timeouts[id];
      if (timeoutId) {
        clearTimeout(timeoutId);
        setTimeouts((prev) => {
          const newTimeouts = { ...prev };
          delete newTimeouts[id];
          return newTimeouts;
        });
      }
    },
    [timeouts],
  );

  // Clear all loading states
  const clearAllLoading = useCallback(() => {
    // Clear all timeouts
    Object.values(timeouts).forEach((timeoutId) => {
      clearTimeout(timeoutId);
    });

    setLoadingStates({});
    setTimeouts({});
  }, [timeouts]);

  // Get loading states by type
  const getLoadingStatesByType = useCallback(
    (type: LoadingType) => {
      return Object.entries(loadingStates)
        .filter(([, state]) => state.type === type && state.isLoading)
        .map(([id, state]) => ({ id, ...state }));
    },
    [loadingStates],
  );

  // Get highest priority loading state
  const getHighestPriorityLoading = useCallback(() => {
    const activeStates = Object.entries(loadingStates)
      .filter(([, state]) => state.isLoading)
      .map(([id, state]) => ({ id, ...state }));

    if (activeStates.length === 0) return null;

    const priorityOrder: LoadingPriority[] = ['critical', 'high', 'medium', 'low'];

    for (const priority of priorityOrder) {
      const stateWithPriority = activeStates.find((state) => state.priority === priority);
      if (stateWithPriority) return stateWithPriority;
    }

    return activeStates[0];
  }, [loadingStates]);

  // Check if any loading is active
  const isAnyLoading = useCallback(() => {
    return Object.values(loadingStates).some((state) => state.isLoading);
  }, [loadingStates]);

  // Check if specific type is loading
  const isTypeLoading = useCallback(
    (type: LoadingType) => {
      return Object.values(loadingStates).some((state) => state.type === type && state.isLoading);
    },
    [loadingStates],
  );

  // Get overall progress (average of all loading states)
  const getOverallProgress = useCallback(() => {
    const activeStates = Object.values(loadingStates).filter((state) => state.isLoading);
    if (activeStates.length === 0) return 100;

    const totalProgress = activeStates.reduce((sum, state) => sum + (state.progress || 0), 0);
    return Math.round(totalProgress / activeStates.length);
  }, [loadingStates]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      Object.values(timeouts).forEach((timeoutId) => {
        clearTimeout(timeoutId);
      });
    };
  }, [timeouts]);

  return useMemo(
    () => ({
      // State
      loadingStates,

      // Actions
      startLoading,
      updateProgress,
      endLoading,
      clearLoading,
      clearAllLoading,

      // Queries
      getLoadingStatesByType,
      getHighestPriorityLoading,
      isAnyLoading,
      isTypeLoading,
      getOverallProgress,

      // Computed properties
      hasActiveLoading: isAnyLoading(),
      highestPriorityState: getHighestPriorityLoading(),
      overallProgress: getOverallProgress(),
    }),
    [
      loadingStates,
      startLoading,
      updateProgress,
      endLoading,
      clearLoading,
      clearAllLoading,
      getLoadingStatesByType,
      getHighestPriorityLoading,
      isAnyLoading,
      isTypeLoading,
      getOverallProgress,
    ],
  );
}
