import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CSSProperties } from 'react';

export type AnimationType = 'fade' | 'slide' | 'scale' | 'grow' | 'blur-fade';
export type NavigationType = 'page' | 'document' | 'initial';

export interface AnimationConfig {
  type: AnimationType;
  duration: number;
  easing: string;
  delay?: number;
}

export interface NavigationAnimationState {
  isAnimating: boolean;
  animationType: NavigationType | null;
  animationId: string | null;
  progress: number; // 0 to 1
}

interface UseNavigationAnimationsOptions {
  // Animation configurations for different navigation types
  pageChangeAnimation?: AnimationConfig;
  documentChangeAnimation?: AnimationConfig;
  initialLoadAnimation?: AnimationConfig;

  // Callbacks
  onAnimationStart?: (type: NavigationType) => void;
  onAnimationEnd?: (type: NavigationType) => void;

  // Control options
  enableAnimations?: boolean;
  forceAnimations?: boolean; // Always animate even if content is cached
}

const DEFAULT_ANIMATIONS: Record<NavigationType, AnimationConfig> = {
  page: {
    type: 'fade',
    duration: 200,
    easing: 'ease-in-out',
  },
  document: {
    type: 'grow',
    duration: 300,
    easing: 'ease-out',
  },
  initial: {
    type: 'fade',
    duration: 400,
    easing: 'ease-in-out',
    delay: 100,
  },
};

/**
 * Hook for managing navigation animations with configurable types and durations.
 * Provides smooth transitions for page changes, document changes, and initial loads.
 */
export function useNavigationAnimations({
  pageChangeAnimation = DEFAULT_ANIMATIONS.page,
  documentChangeAnimation = DEFAULT_ANIMATIONS.document,
  initialLoadAnimation = DEFAULT_ANIMATIONS.initial,
  onAnimationStart,
  onAnimationEnd,
  enableAnimations = true,
  forceAnimations = true,
}: UseNavigationAnimationsOptions = {}) {
  const [animationState, setAnimationState] = useState<NavigationAnimationState>({
    isAnimating: false,
    animationType: null,
    animationId: null,
    progress: 0,
  });

  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Get animation config for navigation type
  const getAnimationConfig = useCallback(
    (type: NavigationType): AnimationConfig => {
      switch (type) {
        case 'page':
          return pageChangeAnimation;
        case 'document':
          return documentChangeAnimation;
        case 'initial':
          return initialLoadAnimation;
        default:
          return DEFAULT_ANIMATIONS.page;
      }
    },
    [pageChangeAnimation, documentChangeAnimation, initialLoadAnimation],
  );

  // Start animation
  const startAnimation = useCallback(
    (type: NavigationType) => {
      if (!enableAnimations) return;

      const config = getAnimationConfig(type);
      const animationId = `${type}-${Date.now()}`;

      // Clear any existing animation
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }

      // Set initial animation state
      setAnimationState({
        isAnimating: true,
        animationType: type,
        animationId,
        progress: 0,
      });

      onAnimationStart?.(type);

      // Start progress tracking
      const startTime = Date.now();
      const updateProgress = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / config.duration, 1);

        setAnimationState((prev) => ({
          ...prev,
          progress,
        }));

        if (progress >= 1) {
          if (progressIntervalRef.current) {
            clearInterval(progressIntervalRef.current);
          }
        }
      };

      progressIntervalRef.current = setInterval(updateProgress, 16); // ~60fps

      // End animation after duration
      animationTimeoutRef.current = setTimeout(
        () => {
          setAnimationState((prev) => ({
            ...prev,
            isAnimating: false,
            animationType: null,
            animationId: null,
            progress: 1,
          }));

          if (progressIntervalRef.current) {
            clearInterval(progressIntervalRef.current);
          }

          onAnimationEnd?.(type);
        },
        config.duration + (config.delay || 0),
      );
    },
    [enableAnimations, getAnimationConfig, onAnimationStart, onAnimationEnd],
  );

  // Stop animation immediately
  const stopAnimation = useCallback(() => {
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }

    setAnimationState({
      isAnimating: false,
      animationType: null,
      animationId: null,
      progress: 1,
    });
  }, []);

  // Generate CSS styles for current animation
  const getAnimationStyles = useCallback(
    (baseStyles: CSSProperties = {}): CSSProperties => {
      if (!animationState.isAnimating || !animationState.animationType) {
        return {
          ...baseStyles,
          opacity: 1,
          transform: 'none',
          filter: 'none',
        };
      }

      const config = getAnimationConfig(animationState.animationType);
      const { progress } = animationState;
      const easeProgress = progress; // Could add easing function here

      let animationStyles: CSSProperties = {};

      switch (config.type) {
        case 'fade':
          animationStyles = {
            opacity: easeProgress,
            transition: `opacity ${config.duration}ms ${config.easing}`,
          };
          break;

        case 'slide':
          animationStyles = {
            transform: `translateX(${(1 - easeProgress) * 20}px)`,
            opacity: easeProgress,
            transition: `transform ${config.duration}ms ${config.easing}, opacity ${config.duration}ms ${config.easing}`,
          };
          break;

        case 'scale':
          animationStyles = {
            transform: `scale(${0.95 + easeProgress * 0.05})`,
            opacity: easeProgress,
            transition: `transform ${config.duration}ms ${config.easing}, opacity ${config.duration}ms ${config.easing}`,
          };
          break;

        case 'grow':
          animationStyles = {
            transform: `scale(${0.98 + easeProgress * 0.02})`,
            opacity: easeProgress,
            transition: `transform ${config.duration}ms ${config.easing}, opacity ${config.duration}ms ${config.easing}`,
          };
          break;

        case 'blur-fade':
          animationStyles = {
            filter: `blur(${(1 - easeProgress) * 2}px)`,
            opacity: easeProgress,
            transition: `filter ${config.duration}ms ${config.easing}, opacity ${config.duration}ms ${config.easing}`,
          };
          break;

        default:
          animationStyles = {
            opacity: easeProgress,
            transition: `opacity ${config.duration}ms ${config.easing}`,
          };
      }

      return {
        ...baseStyles,
        ...animationStyles,
      };
    },
    [animationState, getAnimationConfig],
  );

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  return useMemo(
    () => ({
      // State
      animationState,

      // Actions
      startAnimation,
      stopAnimation,

      // Utilities
      getAnimationStyles,

      // Computed properties
      isAnimating: animationState.isAnimating,
      animationType: animationState.animationType,
      progress: animationState.progress,
    }),
    [animationState, startAnimation, stopAnimation, getAnimationStyles],
  );
}
