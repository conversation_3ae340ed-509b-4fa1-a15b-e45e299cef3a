import { useCallback, useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectPageImagesMap } from '@shared/store/documentSlice';

export interface ImageLoadingState {
  // Image sources
  currentSrc: string | null;
  thumbUrl: string | null;
  imageUrl: string | null;

  // Loading states
  isLoading: boolean;
  isThumbLoaded: boolean;
  isFullImageLoaded: boolean;
  hasError: boolean;

  // Display states
  shouldShowThumbnail: boolean;
  shouldBlur: boolean;
  isReady: boolean;

  // Navigation states
  isNavigating: boolean;
  navigationId: string | null;
}

export interface NavigationContext {
  type: 'page' | 'document' | 'initial';
  fromPageNo?: number;
  toPageNo?: number;
  fromDocId?: string;
  toDocId?: string;
}

interface UseDocumentImageLoaderOptions {
  docId: string;
  pageNo: number;
  onImageReady?: (ready: boolean) => void;
  enablePreloading?: boolean;
  preloadRange?: number; // Number of pages to preload ahead/behind
}

/**
 * Enhanced hook for managing document image loading with thumbnail-first strategy,
 * smooth animations, and comprehensive loading states.
 */
export function useDocumentImageLoader({
  docId,
  pageNo,
  onImageReady,
  enablePreloading = true,
  preloadRange = 2,
}: UseDocumentImageLoaderOptions) {
  // State management
  const [state, setState] = useState<ImageLoadingState>({
    currentSrc: null,
    thumbUrl: null,
    imageUrl: null,
    isLoading: false,
    isThumbLoaded: false,
    isFullImageLoaded: false,
    hasError: false,
    shouldShowThumbnail: false,
    shouldBlur: false,
    isReady: false,
    isNavigating: false,
    navigationId: null,
  });

  // Navigation tracking
  const [navigationContext, setNavigationContext] = useState<NavigationContext | null>(null);
  const [prevDocId, setPrevDocId] = useState<string | null>(null);
  const [prevPageNo, setPrevPageNo] = useState<number | null>(null);
  const isInitialRender = useRef(true);

  // Refs for cleanup and image loading
  const thumbnailImageRef = useRef<HTMLImageElement | null>(null);
  const fullImageRef = useRef<HTMLImageElement | null>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const navigationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get image URLs from Redux store
  const pageImagesMap = useSelector(selectPageImagesMap);
  const imageData = pageImagesMap?.[docId]?.[pageNo];

  // Detect navigation type - split into separate effects to avoid infinite loops
  useEffect(() => {
    // Skip navigation detection on initial render
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }

    let navType: NavigationContext['type'] = 'initial';

    if (prevDocId && prevDocId !== docId) {
      navType = 'document';
    } else if (prevPageNo && prevPageNo !== pageNo) {
      navType = 'page';
    }

    // Only set navigation context if we have previous values to compare
    if (prevDocId !== null || prevPageNo !== null) {
      setNavigationContext({
        type: navType,
        fromDocId: prevDocId || undefined,
        toDocId: docId,
        fromPageNo: prevPageNo || undefined,
        toPageNo: pageNo,
      });

      // Set navigation state with unique ID
      const navigationId = `${navType}-${Date.now()}`;
      setState((prev) => ({
        ...prev,
        isNavigating: true,
        navigationId,
      }));

      // Clear navigation state after animation duration
      if (navigationTimeoutRef.current) {
        clearTimeout(navigationTimeoutRef.current);
      }
      navigationTimeoutRef.current = setTimeout(() => {
        setState((prev) => ({
          ...prev,
          isNavigating: false,
          navigationId: null,
        }));
      }, 300); // Match animation duration
    }
  }, [docId, pageNo]); // Remove prevDocId and prevPageNo from dependencies

  // Update previous values in a separate effect
  useEffect(() => {
    setPrevDocId(docId);
    setPrevPageNo(pageNo);
  }, [docId, pageNo]);

  // Reset state when docId or pageNo changes
  useEffect(() => {
    setState((prev) => ({
      ...prev,
      currentSrc: null,
      isThumbLoaded: false,
      isFullImageLoaded: false,
      hasError: false,
      shouldShowThumbnail: false,
      shouldBlur: false,
      isReady: false,
      isLoading: true,
    }));

    // Clear any existing timeouts
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }

    // Set loading timeout
    loadingTimeoutRef.current = setTimeout(() => {
      setState((prev) => {
        if (!prev.isThumbLoaded && !prev.isFullImageLoaded) {
          return {
            ...prev,
            hasError: true,
            isLoading: false,
          };
        }
        return prev;
      });
    }, 10000); // 10 second timeout

    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, [docId, pageNo]);

  // Update URLs from store
  useEffect(() => {
    setState((prev) => ({
      ...prev,
      thumbUrl: imageData?.thumbUrl || null,
      imageUrl: imageData?.imageUrl || null,
    }));
  }, [imageData]);

  // Load thumbnail
  useEffect(() => {
    if (!state.thumbUrl) return;

    const img = new Image();
    thumbnailImageRef.current = img;

    img.onload = () => {
      setState((prev) => ({
        ...prev,
        currentSrc: state.thumbUrl,
        isThumbLoaded: true,
        shouldShowThumbnail: true,
        isLoading: false,
        isReady: true,
      }));

      // Clear timeout since we have a loaded image
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }

      onImageReady?.(true);
    };

    img.onerror = () => {
      console.warn('Failed to load thumbnail:', state.thumbUrl);
      // Don't set error state yet, we might have a full image
    };

    img.src = state.thumbUrl;

    return () => {
      if (thumbnailImageRef.current) {
        thumbnailImageRef.current.onload = null;
        thumbnailImageRef.current.onerror = null;
        thumbnailImageRef.current = null;
      }
    };
  }, [state.thumbUrl, onImageReady]);

  // Load full image
  useEffect(() => {
    if (!state.imageUrl) return;

    const img = new Image();
    fullImageRef.current = img;

    img.onload = () => {
      // Small delay to prevent flash during transition
      setTimeout(() => {
        setState((prev) => ({
          ...prev,
          currentSrc: state.imageUrl,
          isFullImageLoaded: true,
          shouldShowThumbnail: false,
          shouldBlur: false,
          isLoading: false,
          isReady: true,
        }));

        // Clear timeout since we have a loaded image
        if (loadingTimeoutRef.current) {
          clearTimeout(loadingTimeoutRef.current);
          loadingTimeoutRef.current = null;
        }

        onImageReady?.(true);
      }, 50);
    };

    img.onerror = () => {
      console.warn('Failed to load full image:', state.imageUrl);
      // If we have a thumbnail, that's still okay
      if (!state.isThumbLoaded) {
        setState((prev) => ({
          ...prev,
          hasError: true,
          isLoading: false,
        }));
        onImageReady?.(false);
      }
    };

    img.src = state.imageUrl;

    return () => {
      if (fullImageRef.current) {
        fullImageRef.current.onload = null;
        fullImageRef.current.onerror = null;
        fullImageRef.current = null;
      }
    };
  }, [state.imageUrl, state.isThumbLoaded, onImageReady]);

  // Update blur state
  useEffect(() => {
    setState((prev) => ({
      ...prev,
      shouldBlur: prev.isThumbLoaded && !prev.isFullImageLoaded && !!prev.imageUrl,
    }));
  }, [state.isThumbLoaded, state.isFullImageLoaded, state.imageUrl]);

  // Preloading logic (if enabled)
  useEffect(() => {
    if (!enablePreloading || !pageImagesMap?.[docId]) return;

    const preloadPages = [];
    for (let i = 1; i <= preloadRange; i++) {
      const nextPage = pageNo + i;
      const prevPage = pageNo - i;

      if (nextPage && !pageImagesMap[docId][nextPage]?.imageUrl) {
        preloadPages.push(nextPage);
      }
      if (prevPage > 0 && !pageImagesMap[docId][prevPage]?.imageUrl) {
        preloadPages.push(prevPage);
      }
    }

    // Preload images in background
    preloadPages.forEach((page) => {
      const thumbUrl = pageImagesMap[docId][page]?.thumbUrl;
      const imageUrl = pageImagesMap[docId][page]?.imageUrl;

      // Preload thumbnail first
      if (thumbUrl) {
        const img = new Image();
        img.src = thumbUrl;
      }

      // Then preload full image if available
      if (imageUrl) {
        const img = new Image();
        img.src = imageUrl;
      }
    });
  }, [docId, pageNo, pageImagesMap, enablePreloading, preloadRange]);

  // Handle cached images (images that load immediately)
  const handleCachedImageLoad = useCallback(() => {
    setState((prev) => {
      if (prev.currentSrc) {
        if (loadingTimeoutRef.current) {
          clearTimeout(loadingTimeoutRef.current);
          loadingTimeoutRef.current = null;
        }
        onImageReady?.(true);
        return {
          ...prev,
          isLoading: false,
        };
      }
      return prev;
    });
  }, [onImageReady]);

  // Handle image errors
  const handleImageError = useCallback(() => {
    setState((prev) => {
      console.error('Main image failed to load:', prev.currentSrc);
      onImageReady?.(false);
      return {
        ...prev,
        hasError: true,
        isLoading: false,
      };
    });
  }, [onImageReady]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
      if (navigationTimeoutRef.current) {
        clearTimeout(navigationTimeoutRef.current);
      }
    };
  }, []);

  // Clear navigation context (to be called after processing)
  const clearNavigationContext = useCallback(() => {
    setNavigationContext(null);
  }, []);

  // Public API
  const retry = useCallback(() => {
    setState((prev) => ({
      ...prev,
      hasError: false,
      isLoading: true,
    }));
  }, []);

  const forceReload = useCallback(() => {
    setState((prev) => ({
      ...prev,
      currentSrc: null,
      isThumbLoaded: false,
      isFullImageLoaded: false,
      hasError: false,
      isLoading: true,
    }));
  }, []);

  return {
    // State
    ...state,
    navigationContext,

    // Computed properties
    bestAvailableSource: state.imageUrl || state.thumbUrl,
    hasAnyImage: state.isThumbLoaded || state.isFullImageLoaded,

    // Actions
    retry,
    forceReload,
    clearNavigationContext,

    // Event handlers for component integration
    handleCachedImageLoad,
    handleImageError,
  };
}
