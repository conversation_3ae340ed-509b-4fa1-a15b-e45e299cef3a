export interface Tenant {
  config?: TenantConfig;
  settings?: TenantSettings;
  notice?: TenantNotice;
}

export interface TenantConfig {
  features: TenantFeatures;
  googleLogin: boolean;
  hotjar: boolean;
  squeakyId: boolean;
  subdomain: string;
  multiFactor: boolean;
}

export interface TenantFeatures {
  emailIngestRoute: boolean;
  tempComplex?: boolean;
}
export interface TenantNotice {
  message: string;
  priority: number;
}
export interface TenantSettings {
  allowedDomains?: string[];
  allowedIPs?: string[];
  language: string;
  name: string;
  timezone: string;
  inviteOnly: boolean;
  dashboardRange: number;
  netlifyBeta?: boolean;
}
export interface TenantProvider {
  type: string;
  idpEntityId: string;
  name: string;
  displayName: string;
}
