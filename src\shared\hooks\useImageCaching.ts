import { useCallback, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectPageImagesMap } from '@shared/store/documentSlice';
import { getPageImage, getPageImageThumbs } from '@shared/services/documentService';

interface UseImageCachingOptions {
  docId: string;
  currentPageNo: number;
  preloadRange?: number;
  maxCacheSize?: number;
  enablePreloading?: boolean;
  enableMemoryManagement?: boolean;
}

interface CacheEntry {
  url: string;
  lastAccessed: number;
  size: number; // Estimated size in bytes
}

/**
 * Hook for managing image caching, preloading, and memory management.
 * Integrates with existing Redux store and image loading services.
 */
export function useImageCaching({
  docId,
  currentPageNo,
  preloadRange = 2,
  maxCacheSize = 50 * 1024 * 1024, // 50MB default
  enablePreloading = true,
  enableMemoryManagement = true,
}: UseImageCachingOptions) {
  
  const dispatch = useDispatch();
  const pageImagesMap = useSelector(selectPageImagesMap);
  
  // Cache management
  const cacheRef = useRef<Map<string, CacheEntry>>(new Map());
  const preloadingRef = useRef<Set<string>>(new Set());
  const abortControllersRef = useRef<Map<string, AbortController>>(new Map());

  // Get current document's page data
  const documentPages = pageImagesMap?.[docId] || {};

  // Calculate cache size
  const getCurrentCacheSize = useCallback(() => {
    let totalSize = 0;
    cacheRef.current.forEach(entry => {
      totalSize += entry.size;
    });
    return totalSize;
  }, []);

  // Estimate image size (rough approximation)
  const estimateImageSize = useCallback((url: string, isThumb: boolean = false) => {
    // Rough estimates based on typical image sizes
    if (isThumb) {
      return 50 * 1024; // ~50KB for thumbnails
    }
    return 500 * 1024; // ~500KB for full images
  }, []);

  // Clean up old cache entries when memory limit is reached
  const cleanupCache = useCallback(() => {
    if (!enableMemoryManagement) return;

    const currentSize = getCurrentCacheSize();
    if (currentSize <= maxCacheSize) return;

    // Sort by last accessed time (LRU)
    const entries = Array.from(cacheRef.current.entries())
      .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

    let sizeToRemove = currentSize - maxCacheSize;
    for (const [key, entry] of entries) {
      if (sizeToRemove <= 0) break;
      
      cacheRef.current.delete(key);
      sizeToRemove -= entry.size;
      
      console.log(`Removed cached image: ${key} (${entry.size} bytes)`);
    }
  }, [enableMemoryManagement, getCurrentCacheSize, maxCacheSize]);

  // Update cache entry access time
  const updateCacheAccess = useCallback((url: string) => {
    const entry = cacheRef.current.get(url);
    if (entry) {
      entry.lastAccessed = Date.now();
    }
  }, []);

  // Add to cache
  const addToCache = useCallback((url: string, isThumb: boolean = false) => {
    const size = estimateImageSize(url, isThumb);
    cacheRef.current.set(url, {
      url,
      lastAccessed: Date.now(),
      size,
    });
    
    // Clean up if necessary
    cleanupCache();
  }, [estimateImageSize, cleanupCache]);

  // Check if image is cached
  const isCached = useCallback((url: string) => {
    return cacheRef.current.has(url);
  }, []);

  // Preload image
  const preloadImage = useCallback(async (
    inboxId: string,
    docId: string,
    pageNo: number,
    priority: 'high' | 'low' = 'low'
  ) => {
    const key = `${docId}-${pageNo}`;
    
    // Skip if already preloading or loaded
    if (preloadingRef.current.has(key) || documentPages[pageNo]) {
      return;
    }

    preloadingRef.current.add(key);

    try {
      // Create abort controller for this request
      const controller = new AbortController();
      abortControllersRef.current.set(key, controller);

      // Preload thumbnail first
      if (!documentPages[pageNo]?.thumbUrl) {
        console.log(`Preloading thumbnail for page ${pageNo}`);
        await dispatch(getPageImageThumbs(inboxId, docId));
      }

      // Then preload full image if high priority
      if (priority === 'high' && !documentPages[pageNo]?.imageUrl) {
        console.log(`Preloading full image for page ${pageNo}`);
        await dispatch(getPageImage(inboxId, docId, pageNo));
      }

      // Add to cache tracking
      const thumbUrl = documentPages[pageNo]?.thumbUrl;
      const imageUrl = documentPages[pageNo]?.imageUrl;
      
      if (thumbUrl) addToCache(thumbUrl, true);
      if (imageUrl) addToCache(imageUrl, false);

    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error(`Failed to preload page ${pageNo}:`, error);
      }
    } finally {
      preloadingRef.current.delete(key);
      abortControllersRef.current.delete(key);
    }
  }, [documentPages, dispatch, addToCache]);

  // Cancel preloading for a specific page
  const cancelPreload = useCallback((docId: string, pageNo: number) => {
    const key = `${docId}-${pageNo}`;
    const controller = abortControllersRef.current.get(key);
    
    if (controller) {
      controller.abort();
      abortControllersRef.current.delete(key);
      preloadingRef.current.delete(key);
    }
  }, []);

  // Cancel all preloading
  const cancelAllPreloads = useCallback(() => {
    abortControllersRef.current.forEach(controller => {
      controller.abort();
    });
    abortControllersRef.current.clear();
    preloadingRef.current.clear();
  }, []);

  // Preload adjacent pages
  useEffect(() => {
    if (!enablePreloading || !docId) return;

    const inboxId = 'current-inbox'; // This should come from context/props
    const pagesToPreload: Array<{ pageNo: number; priority: 'high' | 'low' }> = [];

    // Add adjacent pages with priority based on distance
    for (let i = 1; i <= preloadRange; i++) {
      const nextPage = currentPageNo + i;
      const prevPage = currentPageNo - i;
      const priority = i === 1 ? 'high' : 'low';

      if (nextPage && !documentPages[nextPage]) {
        pagesToPreload.push({ pageNo: nextPage, priority });
      }
      if (prevPage > 0 && !documentPages[prevPage]) {
        pagesToPreload.push({ pageNo: prevPage, priority });
      }
    }

    // Start preloading
    pagesToPreload.forEach(({ pageNo, priority }) => {
      preloadImage(inboxId, docId, pageNo, priority);
    });

    // Cleanup: cancel preloads for pages that are now too far away
    const currentPreloads = Array.from(preloadingRef.current);
    currentPreloads.forEach(key => {
      const [preloadDocId, pageNoStr] = key.split('-');
      const pageNo = parseInt(pageNoStr);
      
      if (preloadDocId === docId && Math.abs(pageNo - currentPageNo) > preloadRange) {
        cancelPreload(docId, pageNo);
      }
    });

  }, [docId, currentPageNo, preloadRange, enablePreloading, documentPages, preloadImage, cancelPreload]);

  // Update cache access for current page
  useEffect(() => {
    const currentPageData = documentPages[currentPageNo];
    if (currentPageData?.thumbUrl) {
      updateCacheAccess(currentPageData.thumbUrl);
    }
    if (currentPageData?.imageUrl) {
      updateCacheAccess(currentPageData.imageUrl);
    }
  }, [currentPageNo, documentPages, updateCacheAccess]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cancelAllPreloads();
    };
  }, [cancelAllPreloads]);

  // Cleanup when document changes
  useEffect(() => {
    return () => {
      // Cancel preloads for the previous document
      cancelAllPreloads();
    };
  }, [docId, cancelAllPreloads]);

  return {
    // Cache info
    cacheSize: getCurrentCacheSize(),
    maxCacheSize,
    cacheEntries: cacheRef.current.size,
    
    // Preloading info
    activePreloads: preloadingRef.current.size,
    
    // Actions
    preloadImage,
    cancelPreload,
    cancelAllPreloads,
    cleanupCache,
    isCached,
    
    // Utilities
    updateCacheAccess,
  };
}
