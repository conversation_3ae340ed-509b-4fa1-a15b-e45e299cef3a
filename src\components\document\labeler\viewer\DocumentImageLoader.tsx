import { Ring } from '@uiball/loaders';
import clsx from 'clsx';
import React, { CSSProperties, useCallback, useEffect, useRef, useState } from 'react';
import s from './labeler-view.module.scss';

interface DocumentImageLoaderProps {
  /** The thumbnail URL (lower quality, loads first) */
  thumbUrl?: string;
  /** The full-size image URL (higher quality, loads after thumbnail) */
  imageUrl?: string;
  /** Inline styles for the image */
  style?: CSSProperties;
  /** Ref to the image element */
  imgRef?: React.RefObject<HTMLImageElement>;
  /** Callback when the image is fully loaded and ready */
  onImageReady?: (isReady: boolean) => void;
  /** Whether the viewer should show loading state */
  isViewerLoaded?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Alt text for accessibility */
  alt?: string;
}

/**
 * DocumentImageLoader handles progressive image loading with proper loading states.
 * It loads thumbnails first for quick feedback, then upgrades to full-size images.
 * Provides reliable loading state management and smooth transitions.
 */
const DocumentImageLoader: React.FC<DocumentImageLoaderProps> = ({
  thumbUrl,
  imageUrl,
  style = {},
  imgRef,
  onImageReady,
  isViewerLoaded = false,
  className,
  alt = '',
}) => {
  // Local loading states
  const [currentSrc, setCurrentSrc] = useState<string | null>(null);
  const [isThumbLoaded, setIsThumbLoaded] = useState(false);
  const [isFullImageLoaded, setIsFullImageLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Refs for cleanup
  const fullImageRef = useRef<HTMLImageElement | null>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Determine the best available source
  const bestAvailableSource = imageUrl || thumbUrl;
  const shouldShowFullImage = isFullImageLoaded && imageUrl;
  const shouldShowThumb = isThumbLoaded && thumbUrl && !shouldShowFullImage;
  const hasAnyImage = shouldShowFullImage || shouldShowThumb;

  // Reset states when sources change
  useEffect(() => {
    setIsThumbLoaded(false);
    setIsFullImageLoaded(false);
    setHasError(false);
    setCurrentSrc(null);

    // Only set loading to true if we have a source to load
    if (thumbUrl || imageUrl) {
      setIsLoading(true);
    }

    // Clear any existing timeout
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }

    // Set a timeout to handle cases where images never load
    loadingTimeoutRef.current = setTimeout(() => {
      if (!hasAnyImage && (thumbUrl || imageUrl)) {
        setHasError(true);
        setIsLoading(false);
        onImageReady?.(false);
      }
    }, 10000); // 10 second timeout

    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, [thumbUrl, imageUrl]);

  // Load thumbnail first if available
  useEffect(() => {
    if (!thumbUrl) return;

    const img = new Image();
    img.onload = () => {
      setCurrentSrc(thumbUrl);
      setIsThumbLoaded(true);
      setIsLoading(false);

      // Clear timeout since we have a loaded image
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }

      // Consider ready as soon as thumbnail loads (for faster page navigation)
      onImageReady?.(true);
    };
    img.onerror = () => {
      console.warn('Failed to load thumbnail:', thumbUrl);
      // Don't set error state yet, we might have a full image
    };
    img.src = thumbUrl;

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [thumbUrl, imageUrl, onImageReady]);

  // Load full-size image if available
  useEffect(() => {
    if (!imageUrl) return;

    const img = new Image();
    fullImageRef.current = img;

    img.onload = () => {
      // Add a small delay to prevent flash during transition
      setTimeout(() => {
        setCurrentSrc(imageUrl);
        setIsFullImageLoaded(true);
        setIsLoading(false);

        // Clear timeout since we have a loaded image
        if (loadingTimeoutRef.current) {
          clearTimeout(loadingTimeoutRef.current);
          loadingTimeoutRef.current = null;
        }

        // Full image loaded - always ready
        onImageReady?.(true);
      }, 50); // Small delay to prevent flash
    };
    img.onerror = () => {
      console.warn('Failed to load full image:', imageUrl);
      // If we have a thumbnail, that's still okay - don't call onImageReady again
      if (!isThumbLoaded) {
        setHasError(true);
        setIsLoading(false);
        onImageReady?.(false);
      }
    };
    img.src = imageUrl;

    return () => {
      if (fullImageRef.current) {
        fullImageRef.current.onload = null;
        fullImageRef.current.onerror = null;
        fullImageRef.current = null;
      }
    };
  }, [imageUrl, isThumbLoaded, onImageReady]);

  // Handle main image load event (for cached images)
  const handleMainImageLoad = useCallback(() => {
    if (currentSrc) {
      setIsLoading(false);
      
      // Clear timeout since image is loaded
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }
      
      onImageReady?.(true);
    }
  }, [currentSrc, onImageReady]);

  // Handle main image error
  const handleMainImageError = useCallback(() => {
    console.error('Main image failed to load:', currentSrc);
    setHasError(true);
    setIsLoading(false);
    onImageReady?.(false);
  }, [currentSrc, onImageReady]);

  // Determine if we should show blur effect (when showing thumbnail while full image loads)
  const shouldBlur = isThumbLoaded && !isFullImageLoaded && imageUrl;

  // Calculate final styles with stable sizing
  const imageStyle: CSSProperties = {
    ...style,
    filter: shouldBlur ? 'blur(1px)' : 'none',
    transition: isViewerLoaded
      ? 'filter 0.3s ease-out, opacity 0.3s ease-in-out'
      : 'none',
    opacity: currentSrc && !hasError ? 1 : 0,
    // Prevent size flashing by maintaining consistent sizing
    maxWidth: '100%',
    maxHeight: '100%',
    objectFit: 'contain',
  };

  // If no source is available, show loading state and wait longer for images to load
  useEffect(() => {
    if (!bestAvailableSource) {
      setIsLoading(true);
      // Set a longer timeout to allow for image loading pipeline to complete
      const timeout = setTimeout(() => {
        setIsLoading(false);
        onImageReady?.(false);
      }, 5000); // Longer timeout to allow for thumbnail loading

      return () => clearTimeout(timeout);
    }
  }, [bestAvailableSource, onImageReady]);

  return (
    <>
      {/* Loading indicator */}
      {isLoading && !hasAnyImage && (
        <div className={s.image_loading_overlay}>
          <Ring color={'#0085ff'} size={24} lineWeight={4} />
        </div>
      )}

      {/* Main image - only render if we have a source */}
      {bestAvailableSource && (
        <img
          data-hj-suppress
          ref={imgRef}
          src={currentSrc || bestAvailableSource}
          alt={alt}
          style={imageStyle}
          className={clsx(
            { [s.image]: hasAnyImage && !hasError },
            { [s.image_error]: hasError },
            className,
            'squeaky-hide'
          )}
          loading="eager"
          draggable={false}
          onLoad={handleMainImageLoad}
          onError={handleMainImageError}
        />
      )}

      {/* Error state */}
      {hasError && (
        <div className={s.image_error_message}>
          <span>Failed to load image</span>
        </div>
      )}

      {/* No image available state */}
      {!bestAvailableSource && !isLoading && (
        <div className={s.image_error_message}>
          <span>No image available</span>
        </div>
      )}
    </>
  );
};

export default DocumentImageLoader;
