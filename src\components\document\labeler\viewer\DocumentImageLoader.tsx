import { Ring } from '@uiball/loaders';
import clsx from 'clsx';
import React, { CSSProperties, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectPageImagesMap } from '@shared/store/documentSlice';
import s from './labeler-view.module.scss';

interface DocumentImageLoaderProps {
  /** Document ID */
  docId: string;
  /** Page number */
  pageNo: number;
  /** Inline styles for the image */
  style?: CSSProperties;
  /** Ref to the image element */
  imgRef?: React.RefObject<HTMLImageElement>;
  /** Callback when the image is fully loaded and ready */
  onImageReady?: (isReady: boolean) => void;
  /** Whether the viewer should show loading state */
  isViewerLoaded?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Alt text for accessibility */
  alt?: string;
}

/**
 * Enhanced DocumentImageLoader with thumbnail-first loading and smooth animations.
 * Simplified implementation to avoid infinite loops while maintaining functionality.
 */
const DocumentImageLoader: React.FC<DocumentImageLoaderProps> = ({
  docId,
  pageNo,
  style = {},
  imgRef,
  onImageReady,
  isViewerLoaded = false,
  className,
  alt = '',
}) => {
  // Local state for image loading
  const [currentSrc, setCurrentSrc] = useState<string | null>(null);
  const [isThumbLoaded, setIsThumbLoaded] = useState(false);
  const [isFullImageLoaded, setIsFullImageLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);

  // Refs for cleanup and navigation tracking
  const thumbnailImageRef = useRef<HTMLImageElement | null>(null);
  const fullImageRef = useRef<HTMLImageElement | null>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const navigationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const prevDocIdRef = useRef<string | null>(null);
  const prevPageNoRef = useRef<number | null>(null);

  // Get image URLs from Redux store
  const pageImagesMap = useSelector(selectPageImagesMap);
  const imageData = pageImagesMap?.[docId]?.[pageNo];
  const thumbUrl = imageData?.thumbUrl;
  const imageUrl = imageData?.imageUrl;

  // Detect navigation and trigger animation
  useEffect(() => {
    const prevDocId = prevDocIdRef.current;
    const prevPageNo = prevPageNoRef.current;

    // Skip on initial render
    if (prevDocId === null && prevPageNo === null) {
      prevDocIdRef.current = docId;
      prevPageNoRef.current = pageNo;
      return;
    }

    // Check if navigation occurred
    const isDocumentChange = prevDocId !== null && prevDocId !== docId;
    const isPageChange = prevPageNo !== null && prevPageNo !== pageNo;

    if (isDocumentChange || isPageChange) {
      console.log(`Starting ${isDocumentChange ? 'document' : 'page'} animation`);
      setIsNavigating(true);

      // Clear navigation state after animation duration
      if (navigationTimeoutRef.current) {
        clearTimeout(navigationTimeoutRef.current);
      }
      navigationTimeoutRef.current = setTimeout(() => {
        setIsNavigating(false);
      }, 300);
    }

    // Update refs
    prevDocIdRef.current = docId;
    prevPageNoRef.current = pageNo;
  }, [docId, pageNo]);

  // Reset state when docId or pageNo changes
  useEffect(() => {
    setCurrentSrc(null);
    setIsThumbLoaded(false);
    setIsFullImageLoaded(false);
    setHasError(false);
    setIsLoading(true);

    // Clear any existing timeouts
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }

    // Set loading timeout
    loadingTimeoutRef.current = setTimeout(() => {
      setHasError(true);
      setIsLoading(false);
      onImageReady?.(false);
    }, 10000); // 10 second timeout

    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, [docId, pageNo, onImageReady]);

  // Load thumbnail first
  useEffect(() => {
    if (!thumbUrl) return;

    const img = new Image();
    thumbnailImageRef.current = img;

    img.onload = () => {
      setCurrentSrc(thumbUrl);
      setIsThumbLoaded(true);
      setIsLoading(false);

      // Clear timeout since we have a loaded image
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }

      onImageReady?.(true);
    };

    img.onerror = () => {
      console.warn('Failed to load thumbnail:', thumbUrl);
    };

    img.src = thumbUrl;

    return () => {
      if (thumbnailImageRef.current) {
        thumbnailImageRef.current.onload = null;
        thumbnailImageRef.current.onerror = null;
        thumbnailImageRef.current = null;
      }
    };
  }, [thumbUrl, onImageReady]);

  // Load full image
  useEffect(() => {
    if (!imageUrl) return;

    const img = new Image();
    fullImageRef.current = img;

    img.onload = () => {
      // Small delay to prevent flash during transition
      setTimeout(() => {
        setCurrentSrc(imageUrl);
        setIsFullImageLoaded(true);
        setIsLoading(false);

        // Clear timeout since we have a loaded image
        if (loadingTimeoutRef.current) {
          clearTimeout(loadingTimeoutRef.current);
          loadingTimeoutRef.current = null;
        }

        onImageReady?.(true);
      }, 50);
    };

    img.onerror = () => {
      console.warn('Failed to load full image:', imageUrl);
      if (!isThumbLoaded) {
        setHasError(true);
        setIsLoading(false);
        onImageReady?.(false);
      }
    };

    img.src = imageUrl;

    return () => {
      if (fullImageRef.current) {
        fullImageRef.current.onload = null;
        fullImageRef.current.onerror = null;
        fullImageRef.current = null;
      }
    };
  }, [imageUrl, isThumbLoaded, onImageReady]);

  // Computed values
  const bestAvailableSource = imageUrl || thumbUrl;
  const hasAnyImage = isThumbLoaded || isFullImageLoaded;
  const shouldBlur = isThumbLoaded && !isFullImageLoaded && !!imageUrl;
  const shouldShowLoadingIndicator = (isLoading && !hasAnyImage) || isNavigating;

  // Calculate final image styles with animations
  const imageStyle = useMemo((): CSSProperties => {
    const baseStyles: CSSProperties = {
      ...style,
      maxWidth: '100%',
      maxHeight: '100%',
      objectFit: 'contain',
      filter: shouldBlur ? 'blur(1px)' : 'none',
      transition: isViewerLoaded ? 'filter 0.3s ease-out, opacity 0.3s ease-in-out' : 'none',
      opacity: currentSrc && !hasError ? 1 : 0,
    };

    // Add navigation animation
    if (isNavigating) {
      baseStyles.animation = 'fadeInUp 0.3s ease-out forwards';
    }

    return baseStyles;
  }, [style, shouldBlur, isViewerLoaded, currentSrc, hasError, isNavigating]);

  // Handle main image load event (for cached images)
  const handleMainImageLoad = useCallback(() => {
    if (currentSrc) {
      setIsLoading(false);

      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }

      onImageReady?.(true);
    }
  }, [currentSrc, onImageReady]);

  // Handle main image error
  const handleMainImageError = useCallback(() => {
    console.error('Main image failed to load:', currentSrc);
    setHasError(true);
    setIsLoading(false);
    onImageReady?.(false);
  }, [currentSrc, onImageReady]);

  // Retry function
  const handleRetry = useCallback(() => {
    setHasError(false);
    setIsLoading(true);
    setCurrentSrc(null);
    setIsThumbLoaded(false);
    setIsFullImageLoaded(false);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
      if (navigationTimeoutRef.current) {
        clearTimeout(navigationTimeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      {/* Loading indicator */}
      {shouldShowLoadingIndicator && (
        <div className={s.image_loading_overlay}>
          <Ring color={'#0085ff'} size={24} lineWeight={4} />
          {isNavigating && <div className={s.loading_message}>Loading...</div>}
        </div>
      )}

      {/* Main image */}
      {bestAvailableSource && (
        <img
          data-hj-suppress
          ref={imgRef}
          src={currentSrc || bestAvailableSource}
          alt={alt}
          style={imageStyle}
          className={clsx(
            { [s.image]: hasAnyImage && !hasError },
            { [s.image_error]: hasError },
            className,
            'squeaky-hide',
          )}
          loading="eager"
          draggable={false}
          onLoad={handleMainImageLoad}
          onError={handleMainImageError}
        />
      )}

      {/* Error state */}
      {hasError && (
        <div className={s.image_error_message}>
          <span>Failed to load image</span>
          <button onClick={handleRetry} className={s.retry_button}>
            Retry
          </button>
        </div>
      )}

      {/* No image available state */}
      {!bestAvailableSource && !isLoading && (
        <div className={s.image_error_message}>
          <span>No image available</span>
        </div>
      )}
    </>
  );
};

export default DocumentImageLoader;
