import { Ring } from '@uiball/loaders';
import clsx from 'clsx';
import React, { CSSProperties, useCallback, useEffect, useMemo } from 'react';
import { useDocumentImageLoader } from '@shared/hooks/useDocumentImageLoader';
import { useNavigationAnimations } from '@shared/hooks/useNavigationAnimations';
import { useLoadingStates } from '@shared/hooks/useLoadingStates';
import s from './labeler-view.module.scss';

interface DocumentImageLoaderProps {
  /** Document ID */
  docId: string;
  /** Page number */
  pageNo: number;
  /** Inline styles for the image */
  style?: CSSProperties;
  /** Ref to the image element */
  imgRef?: React.RefObject<HTMLImageElement>;
  /** Callback when the image is fully loaded and ready */
  onImageReady?: (isReady: boolean) => void;
  /** Whether the viewer should show loading state */
  isViewerLoaded?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Alt text for accessibility */
  alt?: string;
  /** Enable preloading of adjacent pages */
  enablePreloading?: boolean;
  /** Enable navigation animations */
  enableAnimations?: boolean;
}

/**
 * Enhanced DocumentImageLoader with thumbnail-first loading, smooth animations,
 * and comprehensive loading state management.
 */
const DocumentImageLoader: React.FC<DocumentImageLoaderProps> = ({
  docId,
  pageNo,
  style = {},
  imgRef,
  onImageReady,
  isViewerLoaded = false,
  className,
  alt = '',
  enablePreloading = true,
  enableAnimations = true,
}) => {
  // Enhanced image loading with thumbnail-first strategy
  const imageLoader = useDocumentImageLoader({
    docId,
    pageNo,
    onImageReady,
    enablePreloading,
  });

  // Navigation animations
  const animations = useNavigationAnimations({
    enableAnimations,
    forceAnimations: true, // Always animate regardless of cache status
    onAnimationStart: (type) => {
      console.log(`Starting ${type} animation`);
    },
    onAnimationEnd: (type) => {
      console.log(`Completed ${type} animation`);
    },
  });

  // Loading state management
  const loadingStates = useLoadingStates({
    onLoadingStart: (type, id) => {
      console.log(`Loading started: ${type} (${id})`);
    },
    onLoadingEnd: (type, id, success) => {
      console.log(`Loading ended: ${type} (${id}) - ${success ? 'success' : 'failed'}`);
    },
  });

  // Handle navigation detection and trigger animations
  useEffect(() => {
    if (imageLoader.navigationContext) {
      const { type } = imageLoader.navigationContext;
      console.log(`Starting ${type} animation`);
      animations.startAnimation(type);

      // Start appropriate loading state
      const loadingId = `${type}-${docId}-${pageNo}`;
      loadingStates.startLoading(loadingId, type === 'document' ? 'document-change' : 'page-change', {
        priority: type === 'document' ? 'high' : 'medium',
        message: type === 'document' ? 'Loading document...' : 'Loading page...',
      });

      // Clear the navigation context to prevent re-processing
      imageLoader.clearNavigationContext();
    }
  }, [imageLoader.navigationContext, animations.startAnimation, loadingStates.startLoading, docId, pageNo, imageLoader.clearNavigationContext]);

  // Handle loading state updates
  useEffect(() => {
    const loadingId = `image-${docId}-${pageNo}`;

    if (imageLoader.isLoading && !imageLoader.hasAnyImage) {
      loadingStates.startLoading(loadingId, 'image', {
        priority: 'medium',
        message: 'Loading image...',
      });
    } else if (imageLoader.isReady) {
      loadingStates.endLoading(loadingId, true);
    } else if (imageLoader.hasError) {
      loadingStates.endLoading(loadingId, false);
    }
  }, [
    imageLoader.isLoading,
    imageLoader.hasAnyImage,
    imageLoader.isReady,
    imageLoader.hasError,
    loadingStates.startLoading,
    loadingStates.endLoading,
    docId,
    pageNo,
  ]);

  // Calculate final image styles with animations
  const imageStyle = useMemo((): CSSProperties => {
    const baseStyles: CSSProperties = {
      ...style,
      // Prevent size flashing by maintaining consistent sizing
      maxWidth: '100%',
      maxHeight: '100%',
      objectFit: 'contain',
    };

    // Apply blur effect when showing thumbnail while full image loads
    if (imageLoader.shouldBlur) {
      baseStyles.filter = 'blur(1px)';
    }

    // Apply navigation animations
    const animatedStyles = animations.getAnimationStyles(baseStyles);

    // Handle opacity based on loading state
    if (!imageLoader.currentSrc || imageLoader.hasError) {
      animatedStyles.opacity = 0;
    } else if (isViewerLoaded) {
      // Add smooth transition when viewer is loaded
      animatedStyles.transition = `${animatedStyles.transition || ''}, opacity 0.3s ease-in-out`.replace(
        /^,\s*/,
        '',
      );
    }

    return animatedStyles;
  }, [
    style,
    imageLoader.shouldBlur,
    imageLoader.currentSrc,
    imageLoader.hasError,
    animations.getAnimationStyles,
    isViewerLoaded,
  ]);

  // Handle cached image load events
  const handleMainImageLoad = useCallback(() => {
    imageLoader.handleCachedImageLoad();
  }, [imageLoader.handleCachedImageLoad]);

  // Handle image error events
  const handleMainImageError = useCallback(() => {
    imageLoader.handleImageError();
  }, [imageLoader.handleImageError]);

  // Determine which loading indicator to show
  const shouldShowLoadingIndicator = useMemo(() => {
    return (imageLoader.isLoading && !imageLoader.hasAnyImage) || animations.isAnimating;
  }, [imageLoader.isLoading, imageLoader.hasAnyImage, animations.isAnimating]);

  // Get the highest priority loading state for display
  const currentLoadingState = loadingStates.highestPriorityState;

  return (
    <>
      {/* Enhanced loading indicator with animation support */}
      {shouldShowLoadingIndicator && (
        <div className={s.image_loading_overlay}>
          <Ring color={'#0085ff'} size={24} lineWeight={4} />
          {currentLoadingState?.message && (
            <div className={s.loading_message}>{currentLoadingState.message}</div>
          )}
        </div>
      )}

      {/* Main image with enhanced loading and animation support */}
      {imageLoader.bestAvailableSource && (
        <img
          data-hj-suppress
          ref={imgRef}
          src={imageLoader.currentSrc || imageLoader.bestAvailableSource}
          alt={alt}
          style={imageStyle}
          className={clsx(
            { [s.image]: imageLoader.hasAnyImage && !imageLoader.hasError },
            { [s.image_error]: imageLoader.hasError },
            className,
            'squeaky-hide',
          )}
          loading="eager"
          draggable={false}
          onLoad={handleMainImageLoad}
          onError={handleMainImageError}
        />
      )}

      {/* Enhanced error state */}
      {imageLoader.hasError && (
        <div className={s.image_error_message}>
          <span>Failed to load image</span>
          <button onClick={imageLoader.retry} className={s.retry_button}>
            Retry
          </button>
        </div>
      )}

      {/* No image available state */}
      {!imageLoader.bestAvailableSource && !imageLoader.isLoading && (
        <div className={s.image_error_message}>
          <span>No image available</span>
        </div>
      )}
    </>
  );
};

export default DocumentImageLoader;
